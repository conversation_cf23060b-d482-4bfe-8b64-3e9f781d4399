import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { InteractiveButton } from "Components/InteractiveButton";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";

const AdminStaffProfileSetup = ({
  onNext,
  register,
  setValue,
  errors,
  defaultValues,
  isSubmitting,
}) => {
  const [phoneValue, setPhoneValue] = useState(defaultValues?.phone || "");

  const schema = yup
    .object({
      first_name: yup.string().required("First name is required"),
      last_name: yup.string().required("Last name is required"),
      email: yup.string().email("Invalid email").required("Email is required"),
      phone: yup.string().required("Phone number is required"),
    })
    .required();

  const {
    register: localRegister,
    handleSubmit,
    formState: { errors: localErrors },
    setValue: localSetValue,
    watch,
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      first_name: defaultValues?.first_name || "",
      last_name: defaultValues?.last_name || "",
      email: defaultValues?.email || "",
      phone: defaultValues?.phone || "",
    },
  });

  const handlePhoneChange = (value) => {
    setPhoneValue(value);
    localSetValue("phone", value);
    setValue("phone", value);
  };

  const onSubmit = async (data) => {
    await onNext(data);
  };

  return (
    <div className="mx-auto max-w-2xl px-6">
      <div className="mb-8 text-center">
        <h1 className="mb-2 text-3xl font-bold text-gray-900">
          Complete Your Profile
        </h1>
        <p className="text-gray-600">
          Let's start by setting up your basic profile information
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          {/* First Name */}
          <div>
            <label
              htmlFor="first_name"
              className="block text-sm font-medium text-gray-700"
            >
              First Name *
            </label>
            <input
              {...localRegister("first_name")}
              type="text"
              id="first_name"
              className="mt-1 block w-full rounded-xl border border-gray-300 px-3 py-3 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-1 focus:ring-primaryBlue"
              placeholder="Enter your first name"
            />
            {localErrors.first_name && (
              <p className="mt-1 text-sm text-red-600">
                {localErrors.first_name.message}
              </p>
            )}
          </div>

          {/* Last Name */}
          <div>
            <label
              htmlFor="last_name"
              className="block text-sm font-medium text-gray-700"
            >
              Last Name *
            </label>
            <input
              {...localRegister("last_name")}
              type="text"
              id="last_name"
              className="mt-1 block w-full rounded-xl border border-gray-300 px-3 py-3 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-1 focus:ring-primaryBlue"
              placeholder="Enter your last name"
            />
            {localErrors.last_name && (
              <p className="mt-1 text-sm text-red-600">
                {localErrors.last_name.message}
              </p>
            )}
          </div>
        </div>

        {/* Email */}
        <div>
          <label
            htmlFor="email"
            className="block text-sm font-medium text-gray-700"
          >
            Email Address *
          </label>
          <input
            {...localRegister("email")}
            type="email"
            id="email"
            disabled
            className="mt-1 block w-full rounded-xl border border-gray-300 bg-gray-50 px-3 py-3 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-1 focus:ring-primaryBlue"
            placeholder="Enter your email address"
          />
          {localErrors.email && (
            <p className="mt-1 text-sm text-red-600">
              {localErrors.email.message}
            </p>
          )}
          <p className="mt-1 text-xs text-gray-500">
            Email cannot be changed during onboarding
          </p>
        </div>

        {/* Phone Number */}
        <div>
          <label
            htmlFor="phone"
            className="block text-sm font-medium text-gray-700"
          >
            Phone Number *
          </label>
          <div className="mt-1">
            <PhoneInput
              country={"us"}
              value={phoneValue}
              onChange={handlePhoneChange}
              inputStyle={{
                width: "100%",
                height: "48px",
                borderRadius: "12px",
                border: "1px solid #d1d5db",
                fontSize: "16px",
                paddingLeft: "48px",
              }}
              containerStyle={{
                width: "100%",
              }}
              buttonStyle={{
                borderRadius: "12px 0 0 12px",
                border: "1px solid #d1d5db",
                borderRight: "none",
              }}
            />
          </div>
          {localErrors.phone && (
            <p className="mt-1 text-sm text-red-600">
              {localErrors.phone.message}
            </p>
          )}
        </div>

        {/* Progress Indicator */}
        <div className="mt-8">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>Step 1 of 3</span>
            <span>Profile Setup</span>
          </div>
          <div className="mt-2 h-2 w-full rounded-full bg-gray-200">
            <div className="h-2 w-1/3 rounded-full bg-primaryGreen"></div>
          </div>
        </div>

        {/* Submit Button */}
        <div className="pt-6">
          <InteractiveButton
            type="submit"
            loading={isSubmitting}
            className="w-full rounded-xl bg-primaryGreen px-4 py-3 text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
          >
            Continue
          </InteractiveButton>
        </div>
      </form>
    </div>
  );
};

export default AdminStaffProfileSetup;
