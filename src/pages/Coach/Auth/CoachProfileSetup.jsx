import { InteractiveButton } from "Components/InteractiveButton";
import React, { useState, useContext, useEffect } from "react";
import AuthLayout from "Src/layout/Auth/AuthLayout";
import { useFieldArray, useForm } from "react-hook-form";
import MkdSDK from "Utils/MkdSDK";
const sdk = new MkdSDK();
import { Navigate, useNavigate } from "react-router-dom";
import { showToast, GlobalContext, getManyByIds } from "Context/Global";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { updateBrowserTab } from "Utils/utils";
import SetupCoachProfile from "Components/CoachProfileSetup/CoachProfile";
import SetupCoachSports from "Components/CoachProfileSetup/CoachSports";
import SetupCoachWorkingHours from "Components/CoachProfileSetup/SetupCoachWorkingHours";
import SetUpCoachBankDetails from "Components/CoachProfileSetup/SetupCoachBankDetails";
import LoadingSpinner from "Components/LoadingSpinner";
import { useClub } from "Context/Club";

// first_name,
// last_name,
// photo,
// phone,
// name,
// bio,
// club_id,
// availability,
// hourly_rate,
// sport_ids,
// account_details,
const days = [
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
  "Sunday",
];
function CoachProfileSetup() {
  const navigate = useNavigate();
  const { state: authState, dispatch: authDispatch } = useContext(AuthContext);
  const [activeStep, setActiveStep] = useState(0);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const { dispatch: globalDispatch, state } = useContext(GlobalContext);
  const { triggerRefetch } = useClub();
  const [clubProfile, setClubProfile] = useState(null);
  const [coachProfile, setCoachProfile] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedTimeSlot, setSelectedTimeSlot] = useState(null);
  const [originalAvailability, setOriginalAvailability] = useState(null);
  const [clubSports, setClubSports] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [stripeConnectionData, setStripeConnectionData] = useState(null);
  const [stripeDataLoading, setStripeDataLoading] = useState(true);
  const userRole = localStorage.getItem("role");

  const determineActiveStep = (profile) => {
    if (!profile) return 0;
    // Check each step in sequence
    if (!profile.bio) return 0;
    if (!profile.sports?.length || !profile.sports) return 1;
    if (!profile.availability?.length) return 2;
    if (
      !profile.account_details ||
      JSON.stringify(profile.account_details).length
    )
      return 3;

    return 4; // If all steps are complete, stay on the last step
  };

  const {
    register,
    setValue,
    formState: { errors },
    watch,
    getValues,
  } = useForm({
    defaultValues: {
      hourly_rate: "",
      bio: "",
      photo: null,
      sports: [],
      availability: [],
      account_details: [],
    },
  });

  const checkStripeConnection = async () => {
    setStripeDataLoading(true);
    try {
      const response = await sdk.callRawAPI(
        `/v3/api/custom/courtmatchup/${userRole}/stripe/account/verify`,
        {},
        "POST"
      );
      setStripeConnectionData(response);
    } catch (error) {
      console.error("Error checking Stripe connection:", error);
      return false;
    } finally {
      setStripeDataLoading(false);
    }
  };

  const fetchCoachProfile = async () => {
    setIsLoading(true);
    try {
      const profile = await sdk.callRawAPI(
        "/v3/api/custom/courtmatchup/coach/profile",
        {},
        "GET"
      );

      await checkStripeConnection();

      if (profile?.completed == 1) {
        navigate("/coach/dashboard");
        return;
      }
      const clubResponse = await sdk.callRawAPI(
        `/v3/api/custom/courtmatchup/user/club/${profile.club_id}`,
        {},
        "GET"
      );

      setCoachProfile(profile);
      setClubProfile(clubResponse.model);
      setClubSports(clubResponse.sports);
      // setActiveStep(determineActiveStep(profile));
      setValue("hourly_rate", profile?.hourly_rate || "");
      setValue("bio", profile?.bio || "");
      setValue("photo", profile?.photo || "");

      const availability = JSON.parse(profile.availability);

      // Initialize with all days having empty timeslots
      const initialAvailability = days.map((day) => ({
        day: day.toLowerCase(),
        timeslots: [],
      }));

      setValue("availability", initialAvailability);
      setValue("account_details", profile?.account_details || []);
      setValue("sports", profile?.sports || []);
      setSelectedTimeSlot(initialAvailability);

      // If there's existing availability, update the corresponding days
      if (
        availability &&
        Array.isArray(availability) &&
        availability.length > 0
      ) {
        availability.forEach((dayData) => {
          const index = initialAvailability.findIndex(
            (day) => day.day === dayData.day.toLowerCase()
          );
          if (index !== -1) {
            initialAvailability[index].timeslots = dayData.timeslots;
          }
        });
      }

      setSelectedTimeSlot(initialAvailability);
      setOriginalAvailability(availability || []);
      //   setValue("sports", profile?.sports?.map((s) => s.sport_id) || []);
      return profile;
    } catch (error) {
      tokenExpireError(authDispatch, error.code);
      showToast(globalDispatch, error.message, 3000, "error");
    } finally {
      setIsLoading(false);
    }
  };

  const handleNext = async (data) => {
    const currentValues = getValues();
    setIsSubmitting(true);
    try {
      // Prepare data based on current step
      const dataToSubmit = {};

      switch (activeStep) {
        case 0: // Profile
          // dataToSubmit.hourly_rate = parseFloat(data.hourly_rate);
          dataToSubmit.bio = data.bio;
          if (data.photo) {
            dataToSubmit.photo = data.photo;
          }
          break;
        case 1: // Sports
          //   console.log(data);
          // dataToSubmit.sport_ids = currentValues.sport_ids;
          break;
        case 2: // Working Hours
          dataToSubmit.availability = currentValues.availability;
          dataToSubmit.default_availability = currentValues.availability;
          break;
        case 3: // Bank Details
          if (data && data.skip_payment) {
            // If coach chooses not to be paid through platform
            dataToSubmit.not_paid_through_platform = 1;
          } else {
            dataToSubmit.not_paid_through_platform = 0;
          }
          dataToSubmit.completed = 1;
          break;
      }

      await sdk.callRawAPI(
        "/v3/api/custom/courtmatchup/coach/profile-edit",
        dataToSubmit,
        "POST"
      );

      const updatedProfile = await fetchCoachProfile();
      setCoachProfile(updatedProfile);

      if (activeStep === 3) {
        // Trigger a refetch of the ClubContext data to update coach_profile
        triggerRefetch();
        setShowSuccessModal(true);
      } else {
        setActiveStep((prev) => prev + 1);
      }
    } catch (error) {
      console.error(error);
      showToast(globalDispatch, error.message, 3000, "error");
      tokenExpireError(authDispatch, error.code);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBack = () => {
    setActiveStep(Math.max(activeStep - 1, 0));
  };

  useEffect(() => {
    (async () => {
      const profile = await fetchCoachProfile();

      if (profile) {
        setActiveStep(determineActiveStep(profile));
      }
    })();
  }, []);

  useEffect(() => {
    updateBrowserTab({
      title: clubProfile?.name,
      path: "/coach/profile-setup",
      clubName: clubProfile?.name,
      favicon: clubProfile?.club_logo,
      description: "Coach Profile Setup",
    });
  }, [clubProfile]);

  const renderStep = () => {
    const values = watch();

    switch (activeStep) {
      case 0:
        return (
          <SetupCoachProfile
            onNext={handleNext}
            register={register}
            setValue={setValue}
            errors={errors}
            defaultValues={values}
            isSubmitting={isSubmitting}
          />
        );
      case 1:
        return (
          <SetupCoachSports
            onNext={handleNext}
            onBack={handleBack}
            register={register}
            setValue={setValue}
            defaultValues={values}
            isSubmitting={isSubmitting}
            clubSports={clubSports}
            refetchCoachProfile={fetchCoachProfile}
          />
        );
      case 2:
        return (
          <SetupCoachWorkingHours
            onNext={handleNext}
            onBack={handleBack}
            selectedTimeSlot={selectedTimeSlot}
            originalAvailability={originalAvailability}
            setSelectedTimeSlot={setSelectedTimeSlot}
            setOriginalAvailability={setOriginalAvailability}
            setValue={setValue}
            defaultValues={values}
            isSubmitting={isSubmitting}
          />
        );

      case 3:
        return (
          <SetUpCoachBankDetails
            onNext={handleNext}
            onBack={handleBack}
            isSubmitting={isSubmitting}
            stripeConnectionData={stripeConnectionData}
            setStripeConnectionData={setStripeConnectionData}
          />
        );

      default:
        return null;
    }
  };

  console.log(activeStep);
  return (
    <AuthLayout>
      {isLoading && <LoadingSpinner />}
      <div className="flex flex-col bg-white pb-7">
        <div className="flex w-full flex-col px-20 pt-4 max-md:max-w-full max-md:px-5">
          <div className="mb-10">
            {activeStep !== 0 && (
              <button
                className="mt-5 flex items-center gap-2 text-[#525866]"
                onClick={handleBack}
              >
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 20 20"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M8.33333 4.79199L3.125 10.0003L8.33333 15.2087M3.75 10.0003H16.875"
                    stroke="#525866"
                    stroke-width="1.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <span>Back</span>
              </button>
            )}
          </div>
        </div>
        <div className="">{renderStep()}</div>
        {/* Success Modal */}
        {showSuccessModal && (
          <div className="fixed inset-0 z-[999] flex items-center justify-center bg-black bg-opacity-50">
            <div className="w-full max-w-xl rounded-2xl bg-white ">
              <div className="flex flex-col items-center">
                <div className="flex items-start gap-4 p-5">
                  <div className="">
                    <svg
                      width="40"
                      height="40"
                      viewBox="0 0 40 40"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <rect width="40" height="40" rx="10" fill="#EFFAF6" />
                      <path
                        d="M20 29C15.0293 29 11 24.9707 11 20C11 15.0293 15.0293 11 20 11C24.9707 11 29 15.0293 29 20C29 24.9707 24.9707 29 20 29ZM19.1027 23.6L25.4657 17.2361L24.1931 15.9635L19.1027 21.0548L16.5566 18.5087L15.284 19.7813L19.1027 23.6Z"
                        fill="#38C793"
                      />
                    </svg>
                  </div>
                  <div>
                    <h2 className="mb-4 text-2xl font-medium">
                      Sign up complete!
                    </h2>
                    <p className="mb-6 text-gray-600">
                      Congratulations! You have successfully signed up for Court
                      Matchup. You can now access your coach portal
                    </p>
                  </div>
                </div>
                <div className="flex w-full justify-end border-t border-gray-200 p-5">
                  <InteractiveButton
                    onClick={() => {
                      // Trigger another refetch before navigating to dashboard
                      triggerRefetch();
                      navigate("/coach/dashboard");
                    }}
                    className="w-fit rounded-xl bg-primaryGreen px-5 py-3 text-white"
                  >
                    Continue to Coach portal!
                  </InteractiveButton>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </AuthLayout>
  );
}

export default CoachProfileSetup;
