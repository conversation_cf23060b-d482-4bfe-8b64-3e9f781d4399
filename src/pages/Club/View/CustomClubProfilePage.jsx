import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "Utils/MkdSDK";
import { GlobalContext, showToast } from "Context/Global";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { InteractiveButton } from "Components/InteractiveButton";
import LoadingSpinner from "Components/LoadingSpinner";

let sdk = new MkdSDK();
const CustomClubProfilePage = () => {
  const schema = yup
    .object({
      email: yup.string().email().required(),
    })
    .required();

  const { dispatch } = React.useContext(AuthContext);
  const [oldPhoto, setOldPhoto] = useState("");
  const [submitLoading, setSubmitLoading] = useState(false);
  const [defaultValues, setDefaultValues] = useState({});
  const [loading, setLoading] = useState(true);
  const [editingField, setEditingField] = useState(null);
  const [editValue, setEditValue] = useState("");
  const [profilePictureLoading, setProfilePictureLoading] = useState(false);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { setError, setValue } = useForm({
    resolver: yupResolver(schema),
  });

  async function fetchData() {
    setLoading(true);
    try {
      const result = await sdk.getProfile();
      if (!result.error) {
        setDefaultValues(result);
        setValue("email", result?.email);
        setValue("first_name", result?.first_name);
        setValue("last_name", result?.last_name);
        setOldPhoto(result?.photo);
        dispatch({
          type: "UPDATE_PROFILE",
          payload: result,
        });
        setLoading(false);
      }
    } catch (error) {
      tokenExpireError(
        dispatch,
        error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }

  // Helper function to handle validation errors
  const handleValidationErrors = (result) => {
    if (result.validation) {
      const keys = Object.keys(result.validation);
      for (let i = 0; i < keys.length; i++) {
        const field = keys[i];
        setError(field, {
          type: "manual",
          message: result.validation[field],
        });
      }
    }
  };

  const handleEdit = async (field, value) => {
    try {
      setSubmitLoading(true);

      // Create data object with the field being edited
      const data = {
        [field]: value,
      };

      if (field === "email") {
        // Handle email update
        const emailResult = await sdk.updateEmail(value);
        if (!emailResult.error) {
          showToast(globalDispatch, "Email Updated", 1000);
          setEditingField(null);
          setEditValue("");
          fetchData();
        } else {
          handleValidationErrors(emailResult);
        }
      } else if (field === "password") {
        // Handle password update
        const passwordResult = await sdk.updatePassword(value);
        if (!passwordResult.error) {
          showToast(globalDispatch, "Password Updated", 2000);
          setEditingField(null);
          setEditValue("");
        } else {
          handleValidationErrors(passwordResult);
        }
      } else {
        // Handle other profile fields
        const result = await sdk.updateProfile({
          ...data,
        });

        if (!result.error) {
          showToast(globalDispatch, "Profile Updated", 4000);
          setEditingField(null);
          setEditValue("");
          fetchData();
        } else {
          handleValidationErrors(result);
        }
      }

      setSubmitLoading(false);
    } catch (error) {
      setSubmitLoading(false);
      setError(field, {
        type: "manual",
        message: error.response?.data?.message || error.message,
      });
      tokenExpireError(
        dispatch,
        error.response?.data?.message || error.message
      );
    }
  };

  const handleChangePhoto = async (file) => {
    try {
      setProfilePictureLoading(true);
      let formData = new FormData();
      formData.append("file", file);
      let uploadResult = await sdk.uploadImage(formData);

      // Update profile with new photo
      const result = await sdk.updateProfile({
        photo: uploadResult.url,
      });

      if (!result.error) {
        showToast(globalDispatch, "Profile Photo Updated", 1000);
        setOldPhoto(uploadResult.url);
        fetchData();
      }
    } catch (error) {
      showToast(globalDispatch, error?.message, 3000, "error");
      console.log(error);
    } finally {
      setProfilePictureLoading(false);
    }
  };

  const handleRemovePhoto = async () => {
    try {
      setProfilePictureLoading(true);

      // Update profile with null photo
      const result = await sdk.updateProfile({
        photo: null,
      });

      if (!result.error) {
        showToast(globalDispatch, "Profile Photo Removed", 1000);
        setOldPhoto(null);
        fetchData();
      }
    } catch (error) {
      showToast(globalDispatch, error?.message, 3000, "error");
      console.log(error);
    } finally {
      setProfilePictureLoading(false);
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "profile",
      },
    });

    fetchData();
  }, [globalDispatch]);

  return (
    <div className="p-5">
      {loading || (profilePictureLoading && <LoadingSpinner />)}
      <div className="">
        <div className="">
          <div className="mb-6">
            <h2 className="text-xl font-semibold text-gray-900">
              Profile details
            </h2>
          </div>

          <div className="max-w-lg space-y-6">
            {/* Profile Picture Section */}
            <div className="flex items-center gap-4">
              <img
                src={oldPhoto || "/default-avatar.png"}
                alt="Profile"
                className="h-24 w-24 rounded-full object-cover"
              />
              <div>
                <div className="flex flex-col items-start justify-between gap-2">
                  <p className="font-medium text-gray-700">Profile Picture</p>
                  <div className="flex gap-2">
                    <button
                      onClick={handleRemovePhoto}
                      disabled={!oldPhoto}
                      className="rounded-xl border border-red-600 px-3 py-1.5 text-red-600 disabled:opacity-50"
                    >
                      Remove
                    </button>
                    <label className="cursor-pointer rounded-xl border border-gray-300 px-3 py-1.5 text-gray-700">
                      Change Photo
                      <input
                        type="file"
                        accept="image/*"
                        onChange={(e) => handleChangePhoto(e.target.files[0])}
                        className="hidden"
                      />
                    </label>
                  </div>
                  <p className="text-xs text-gray-500">
                    Min 400x400px, PNG or JPEG
                  </p>
                </div>
              </div>
            </div>

            {/* Personal Information */}
            <div className="space-y-4">
              {[
                { key: "first_name", label: "First Name" },
                { key: "last_name", label: "Last Name" },
                {
                  key: "email",
                  label: "Email",
                  note: "Your email is not shared with other users.",
                },
                { key: "password", label: "Password", type: "password" },
              ].map((field) => (
                <div key={field.key}>
                  {editingField === field.key ? (
                    <div>
                      <label className="flex justify-between">
                        <span className="font-medium text-gray-700">
                          {field.label}
                        </span>
                        <button
                          onClick={() => setEditingField(null)}
                          className="text-primaryBlue underline hover:text-indigo-800"
                        >
                          Cancel
                        </button>
                      </label>
                      {field.type === "password" ? (
                        <input
                          type="password"
                          value={editValue}
                          onChange={(e) => setEditValue(e.target.value)}
                          className="mt-1 w-full rounded-xl border border-gray-300 p-2"
                          placeholder="Enter new password"
                        />
                      ) : (
                        <input
                          type="text"
                          value={editValue}
                          onChange={(e) => setEditValue(e.target.value)}
                          className="mt-1 w-full rounded-xl border border-gray-300 p-2"
                        />
                      )}
                      {field.note && (
                        <p className="mt-1 text-xs text-gray-500">
                          {field.note}
                        </p>
                      )}
                      <div className="mt-2">
                        <InteractiveButton
                          loading={submitLoading}
                          onClick={() => handleEdit(field.key, editValue)}
                          className="rounded-xl bg-primaryBlue px-4 py-2 font-medium text-white hover:bg-primaryBlue"
                        >
                          Save
                        </InteractiveButton>
                      </div>
                    </div>
                  ) : (
                    <div>
                      <div className="flex items-center justify-between">
                        <p className="font-medium text-gray-500">
                          {field.label}
                        </p>
                        <button
                          onClick={() => {
                            setEditingField(field.key);
                            setEditValue(
                              field.key === "password"
                                ? ""
                                : defaultValues?.[field.key] || ""
                            );
                          }}
                          className="text-primaryBlue hover:text-indigo-800"
                        >
                          Edit
                        </button>
                      </div>
                      <p className="mt-1">
                        {field.key === "password"
                          ? "••••••••"
                          : defaultValues?.[field.key] || "--"}
                      </p>
                      {field.note && (
                        <p className="mt-1 text-xs text-gray-500">
                          {field.note}
                        </p>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomClubProfilePage;
