import { InteractiveButton } from "Components/InteractiveButton";
import React, { useState, useContext, useEffect } from "react";
import AuthLayout from "Src/layout/Auth/AuthLayout";
import { useForm } from "react-hook-form";
import MkdSDK from "Utils/MkdSDK";
const sdk = new MkdSDK();
import { useNavigate } from "react-router-dom";
import { showToast, GlobalContext } from "Context/Global";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { updateBrowserTab } from "Utils/utils";
import LoadingSpinner from "Components/LoadingSpinner";
import AdminStaffProfileSetup from "Components/AdminStaffOnboarding/AdminStaffProfileSetup";
import AdminStaffPasswordSetup from "Components/AdminStaffOnboarding/AdminStaffPasswordSetup";
import AdminStaffBankSetup from "Components/AdminStaffOnboarding/AdminStaffBankSetup";

function AdminStaffOnboarding() {
  const navigate = useNavigate();
  const { dispatch: authDispatch } = useContext(AuthContext);
  const [activeStep, setActiveStep] = useState(0);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [stripeConnectionData, setStripeConnectionData] = useState(null);
  const [profileData, setProfileData] = useState(null);

  const determineActiveStep = (profile) => {
    if (!profile) return 0;
    // Check each step in sequence
    if (!profile.first_name || !profile.last_name || !profile.phone) return 0;
    if (!profile.password_changed) return 1;
    if (!profile.stripe_connected) return 2;
    return 3; // If all steps are complete, stay on the last step
  };

  const {
    register,
    setValue,
    formState: { errors },
    watch,
  } = useForm({
    defaultValues: {
      first_name: "",
      last_name: "",
      email: "",
      phone: "",
      password: "",
      confirm_password: "",
    },
  });

  const checkStripeConnection = async () => {
    try {
      const response = await sdk.callRawAPI(
        `/v3/api/custom/courtmatchup/admin_staff/stripe/account/verify`,
        {},
        "POST"
      );
      setStripeConnectionData(response);
    } catch (error) {
      console.error("Error checking Stripe connection:", error);
      return false;
    }
  };

  const fetchAdminStaffProfile = async () => {
    setIsLoading(true);
    try {
      const profile = await sdk.callRawAPI(
        "/v3/api/custom/courtmatchup/admin-staff/profile",
        {},
        "GET"
      );

      await checkStripeConnection();

      if (profile?.completed == 1) {
        navigate("/admin-staff/dashboard");
        return;
      }

      // Store profile data for components
      setProfileData(profile);

      setValue("first_name", profile?.first_name || "");
      setValue("last_name", profile?.last_name || "");
      setValue("email", profile?.email || "");
      setValue("phone", profile?.phone || "");

      return profile;
    } catch (error) {
      tokenExpireError(authDispatch, error.code);
      showToast(globalDispatch, error.message, 3000, "error");
    } finally {
      setIsLoading(false);
    }
  };

  const handleNext = async (data) => {
    setIsSubmitting(true);
    try {
      // Prepare data based on current step
      const dataToSubmit = {};

      switch (activeStep) {
        case 0: // Profile Setup
          dataToSubmit.first_name = data.first_name;
          dataToSubmit.last_name = data.last_name;
          dataToSubmit.phone = data.phone;
          break;
        case 1: // Password Setup
          dataToSubmit.password = data.password;
          dataToSubmit.password_changed = 1;
          break;
        case 2: // Bank Details
          if (data && data.skip_payment) {
            dataToSubmit.not_paid_through_platform = 1;
          } else {
            dataToSubmit.not_paid_through_platform = 0;
          }
          dataToSubmit.completed = 1;
          break;
      }

      await sdk.callRawAPI(
        "/v3/api/custom/courtmatchup/admin-staff/profile-edit",
        dataToSubmit,
        "POST"
      );

      await fetchAdminStaffProfile();

      if (activeStep === 2) {
        setShowSuccessModal(true);
      } else {
        setActiveStep((prev) => prev + 1);
      }
    } catch (error) {
      console.error(error);
      showToast(globalDispatch, error.message, 3000, "error");
      tokenExpireError(authDispatch, error.code);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBack = () => {
    setActiveStep(Math.max(activeStep - 1, 0));
  };

  useEffect(() => {
    (async () => {
      const profile = await fetchAdminStaffProfile();

      if (profile) {
        setActiveStep(determineActiveStep(profile));
      }
    })();
  }, []);

  useEffect(() => {
    updateBrowserTab({
      title: "Admin Staff Onboarding",
      path: "/admin-staff/onboarding",
      description: "Admin Staff Onboarding",
    });
  }, []);

  const renderStep = () => {
    const values = watch();

    switch (activeStep) {
      case 0:
        return (
          <AdminStaffProfileSetup
            onNext={handleNext}
            setValue={setValue}
            defaultValues={profileData || values}
            isSubmitting={isSubmitting}
          />
        );
      case 1:
        return (
          <AdminStaffPasswordSetup
            onNext={handleNext}
            onBack={handleBack}
            register={register}
            setValue={setValue}
            errors={errors}
            defaultValues={profileData || values}
            isSubmitting={isSubmitting}
          />
        );
      case 2:
        return (
          <AdminStaffBankSetup
            onNext={handleNext}
            onBack={handleBack}
            isSubmitting={isSubmitting}
            stripeConnectionData={stripeConnectionData}
            setStripeConnectionData={setStripeConnectionData}
          />
        );

      default:
        return null;
    }
  };

  return (
    <AuthLayout>
      {isLoading && <LoadingSpinner />}
      <div className="flex flex-col bg-white pb-7">
        <div className="flex w-full flex-col px-20 pt-4 max-md:max-w-full max-md:px-5">
          <div className="mb-10">
            {activeStep !== 0 && (
              <button
                className="mt-5 flex items-center gap-2 text-[#525866]"
                onClick={handleBack}
              >
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 20 20"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M8.33333 4.79199L3.125 10.0003L8.33333 15.2087M3.75 10.0003H16.875"
                    stroke="#525866"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                <span>Back</span>
              </button>
            )}
          </div>
        </div>
        <div className="">{renderStep()}</div>
        {/* Success Modal */}
        {showSuccessModal && (
          <div className="fixed inset-0 z-[999] flex items-center justify-center bg-black bg-opacity-50">
            <div className="w-full max-w-xl rounded-2xl bg-white ">
              <div className="flex flex-col items-center">
                <div className="flex items-start gap-4 p-5">
                  <div className="">
                    <svg
                      width="40"
                      height="40"
                      viewBox="0 0 40 40"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <rect width="40" height="40" rx="10" fill="#EFFAF6" />
                      <path
                        d="M20 29C15.0293 29 11 24.9707 11 20C11 15.0293 15.0293 11 20 11C24.9707 11 29 15.0293 29 20C29 24.9707 24.9707 29 20 29ZM19.1027 23.6L25.4657 17.2361L24.1931 15.9635L19.1027 21.0548L16.5566 18.5087L15.284 19.7813L19.1027 23.6Z"
                        fill="#38C793"
                      />
                    </svg>
                  </div>
                  <div>
                    <h2 className="mb-4 text-2xl font-medium">
                      Onboarding complete!
                    </h2>
                    <p className="mb-6 text-gray-600">
                      Congratulations! You have successfully completed your
                      admin staff onboarding. You can now access your admin
                      staff portal.
                    </p>
                  </div>
                </div>
                <div className="flex w-full justify-end border-t border-gray-200 p-5">
                  <InteractiveButton
                    onClick={() => {
                      navigate("/admin-staff/dashboard");
                    }}
                    className="w-fit rounded-xl bg-primaryGreen px-5 py-3 text-white"
                  >
                    Continue to Admin Staff portal!
                  </InteractiveButton>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </AuthLayout>
  );
}

export default AdminStaffOnboarding;
